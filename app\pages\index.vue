<template>
  <UContainer class="py-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        Business Analyst Report
      </h1>
      <p class="text-lg text-gray-600 dark:text-gray-300 mb-2">
        Performance Task #2: Exhibit B - The Business Analyst Report
      </p>
      <p class="text-sm text-gray-500 dark:text-gray-400">
        General Mathematics 11 - Innovatech Nexus-Pebble Analysis
      </p>
    </div>

    <!-- Analyst ID Input -->
    <UCard class="mb-8">
      <template #header>
        <h2 class="text-xl font-semibold">Your Analyst ID</h2>
      </template>

      <div class="space-y-4">
        <p class="text-gray-600 dark:text-gray-300">
          Enter your official class roll number as your Analyst ID Number (A):
        </p>

        <UFormGroup label="Analyst ID (A)" name="analystId">
          <UInput
            v-model.number="analystId"
            type="number"
            placeholder="Enter your class roll number"
            min="1"
            max="50"
            size="lg"
          />
        </UFormGroup>

        <div v-if="analystId > 0" class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <p class="text-green-700 dark:text-green-300 font-medium">
            ✓ Analyst ID set to: {{ analystId }}
          </p>
          <p class="text-sm text-green-600 dark:text-green-400 mt-1">
            All calculations below will use A = {{ analystId }}
          </p>
        </div>
      </div>
    </UCard>

    <!-- Tasks Display -->
    <div v-if="analystId > 0" class="space-y-8">
      <!-- Task 1 -->
      <TaskCard :task="task1" />

      <!-- Task 2 -->
      <TaskCard :task="task2" />

      <!-- Task 3 -->
      <TaskCard :task="task3" />

      <!-- Task 4 -->
      <TaskCard :task="task4" />

      <!-- Task 5 -->
      <TaskCard :task="task5" />
    </div>

    <!-- Instructions when no ID entered -->
    <UCard v-else class="text-center">
      <div class="py-8">
        <UIcon name="i-heroicons-calculator" class="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Enter Your Analyst ID
        </h3>
        <p class="text-gray-500 dark:text-gray-400">
          Please enter your class roll number above to see all calculations and solutions.
        </p>
      </div>
    </UCard>
  </UContainer>
</template>

<script setup lang="ts">
const { analystId, task1, task2, task3, task4, task5 } = useBusinessAnalysis()

// Set page meta
definePageMeta({
  title: 'Business Analyst Report - Performance Task #2'
})

// SEO
useSeoMeta({
  title: 'Business Analyst Report - Performance Task #2',
  description: 'Interactive business analysis tool for General Mathematics 11 performance task',
  ogTitle: 'Business Analyst Report - Performance Task #2',
  ogDescription: 'Interactive business analysis tool for General Mathematics 11 performance task'
})
</script>