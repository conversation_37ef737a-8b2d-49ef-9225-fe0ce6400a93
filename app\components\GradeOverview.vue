<template>
  <div class="space-y-6">
    <!-- Class Statistics -->
    <UCard>
      <template #header>
        <h2 class="text-xl font-semibold">Class Statistics</h2>
      </template>
      
      <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div class="text-center">
          <div class="text-2xl font-bold text-primary">{{ stats.totalStudents }}</div>
          <div class="text-sm text-gray-500">Students Graded</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ stats.averageScore }}</div>
          <div class="text-sm text-gray-500">Average Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ stats.averagePercentage }}%</div>
          <div class="text-sm text-gray-500">Average %</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ stats.highestScore }}</div>
          <div class="text-sm text-gray-500">Highest Score</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-orange-600">{{ stats.lowestScore }}</div>
          <div class="text-sm text-gray-500">Lowest Score</div>
        </div>
      </div>

      <!-- Grade Distribution -->
      <div class="mt-6">
        <h3 class="font-medium mb-3">Grade Distribution</h3>
        <div class="grid grid-cols-5 gap-2">
          <div v-for="(count, grade) in stats.gradeDistribution" :key="grade" class="text-center">
            <div class="text-lg font-bold" :class="getGradeColor(grade)">{{ count }}</div>
            <div class="text-xs text-gray-500">{{ grade }}</div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Search and Filters -->
    <UCard>
      <div class="flex flex-col md:flex-row gap-4">
        <UInput
          v-model="searchQuery"
          placeholder="Search by Analyst ID..."
          icon="i-heroicons-magnifying-glass"
          class="flex-1"
        />
        <USelect
          v-model="filterGrade"
          :options="gradeFilterOptions"
          placeholder="Filter by grade"
          class="w-full md:w-48"
        />
        <UButton @click="exportGrades" color="neutral" variant="outline">
          Export CSV
        </UButton>
      </div>
    </UCard>

    <!-- Grades Table -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold">All Grades</h2>
          <div class="text-sm text-gray-500">
            {{ filteredGrades.length }} of {{ allGrades.length }} students
          </div>
        </div>
      </template>

      <div v-if="filteredGrades.length === 0" class="text-center py-8">
        <UIcon name="i-heroicons-document-text" class="w-12 h-12 mx-auto text-gray-400 mb-4" />
        <p class="text-gray-500">No grades found matching your criteria.</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-3 px-4 font-medium">Analyst ID</th>
              <th class="text-center py-3 px-4 font-medium">Score</th>
              <th class="text-center py-3 px-4 font-medium">Percentage</th>
              <th class="text-center py-3 px-4 font-medium">Grade</th>
              <th class="text-center py-3 px-4 font-medium">Graded Date</th>
              <th class="text-center py-3 px-4 font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="grade in filteredGrades" 
              :key="grade.analystId"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50"
            >
              <td class="py-3 px-4 font-mono">{{ grade.analystId }}</td>
              <td class="py-3 px-4 text-center font-bold">{{ grade.totalScore }}/60</td>
              <td class="py-3 px-4 text-center">{{ Math.round((grade.totalScore / 60) * 100) }}%</td>
              <td class="py-3 px-4 text-center">
                <UBadge 
                  :color="getGradeBadgeColor(getLetterGrade(grade.totalScore))"
                  variant="solid"
                >
                  {{ getLetterGrade(grade.totalScore) }}
                </UBadge>
              </td>
              <td class="py-3 px-4 text-center text-sm text-gray-500">
                {{ formatDate(grade.gradedAt) }}
              </td>
              <td class="py-3 px-4 text-center">
                <div class="flex items-center justify-center gap-2">
                  <UButton
                    @click="viewGrade(grade)"
                    size="xs"
                    color="info"
                    variant="outline"
                  >
                    View
                  </UButton>
                  <UButton
                    @click="editGrade(grade.analystId)"
                    size="xs"
                    color="neutral"
                    variant="outline"
                  >
                    Edit
                  </UButton>
                  <UButton
                    @click="confirmDelete(grade.analystId)"
                    size="xs"
                    color="error"
                    variant="outline"
                  >
                    Delete
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </UCard>

    <!-- Grade Detail Modal -->
    <UModal v-model="showGradeDetail">
      <UCard v-if="selectedGrade">
        <template #header>
          <h3 class="text-lg font-semibold">
            Grade Details - Analyst ID {{ selectedGrade.analystId }}
          </h3>
        </template>

        <div class="space-y-4">
          <div class="grid grid-cols-3 gap-4 text-sm">
            <div><strong>Total Score:</strong> {{ selectedGrade.totalScore }}/60</div>
            <div><strong>Percentage:</strong> {{ Math.round((selectedGrade.totalScore / 60) * 100) }}%</div>
            <div><strong>Letter Grade:</strong> {{ getLetterGrade(selectedGrade.totalScore) }}</div>
          </div>

          <div>
            <h4 class="font-medium mb-2">Criterion Scores:</h4>
            <div class="space-y-2">
              <div 
                v-for="criterion in selectedGrade.criteria" 
                :key="criterion.criterionId"
                class="flex justify-between items-center text-sm"
              >
                <span>{{ getCriterionName(criterion.criterionId) }}</span>
                <span class="font-mono">{{ criterion.score }}/10</span>
              </div>
            </div>
          </div>

          <div v-if="selectedGrade.overallComment">
            <h4 class="font-medium mb-2">Overall Comments:</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-3 rounded">
              {{ selectedGrade.overallComment }}
            </p>
          </div>
        </div>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import type { StudentGrade } from '~/types/grading'

const { 
  allGrades, 
  getClassStatistics, 
  deleteGrade,
  rubricCriteria
} = useGrading()

// Reactive state
const searchQuery = ref('')
const filterGrade = ref('')
const showGradeDetail = ref(false)
const selectedGrade = ref<StudentGrade | null>(null)

// Computed properties
const stats = getClassStatistics

const gradeFilterOptions = [
  { label: 'All Grades', value: '' },
  { label: 'A (90-100%)', value: 'A' },
  { label: 'B (80-89%)', value: 'B' },
  { label: 'C (70-79%)', value: 'C' },
  { label: 'D (60-69%)', value: 'D' },
  { label: 'F (0-59%)', value: 'F' }
]

const filteredGrades = computed(() => {
  let filtered = [...allGrades.value]

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(grade =>
      grade.analystId.toString().includes(query)
    )
  }

  // Grade filter
  if (filterGrade.value) {
    filtered = filtered.filter(grade => 
      getLetterGrade(grade.totalScore) === filterGrade.value
    )
  }

  // Sort by analyst ID
  return filtered.sort((a, b) => a.analystId - b.analystId)
})

// Helper functions
const getLetterGrade = (score: number) => {
  const percentage = Math.round((score / 60) * 100)
  if (percentage >= 90) return 'A'
  if (percentage >= 80) return 'B'
  if (percentage >= 70) return 'C'
  if (percentage >= 60) return 'D'
  return 'F'
}

const getGradeColor = (grade: string) => {
  const colors = {
    A: 'text-green-600',
    B: 'text-blue-600',
    C: 'text-yellow-600',
    D: 'text-orange-600',
    F: 'text-red-600'
  }
  return colors[grade as keyof typeof colors] || 'text-gray-600'
}

const getGradeBadgeColor = (grade: string) => {
  const colors = {
    A: 'green',
    B: 'blue',
    C: 'yellow',
    D: 'orange',
    F: 'red'
  }
  return colors[grade as keyof typeof colors] || 'gray'
}

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const getCriterionName = (criterionId: string) => {
  const criterion = rubricCriteria.find(c => c.id === criterionId)
  return criterion?.name || criterionId
}

// Actions
const viewGrade = (grade: StudentGrade) => {
  selectedGrade.value = grade
  showGradeDetail.value = true
}

const editGrade = (analystId: number) => {
  // Emit event to parent to switch to grading interface
  emit('editGrade', analystId)
}

const confirmDelete = (analystId: number) => {
  if (confirm(`Are you sure you want to delete the grade for Analyst ID ${analystId}?`)) {
    deleteGrade(analystId)
  }
}

const exportGrades = () => {
  // Create CSV content
  const headers = ['Analyst ID', 'Total Score', 'Percentage', 'Letter Grade', 'Graded Date']
  const rows = filteredGrades.value.map(grade => [
    grade.analystId,
    grade.totalScore,
    Math.round((grade.totalScore / 60) * 100),
    getLetterGrade(grade.totalScore),
    formatDate(grade.gradedAt)
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n')

  // Download CSV
  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `business-analyst-grades-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  URL.revokeObjectURL(url)
}

// Emits
interface Emits {
  editGrade: [analystId: number]
}

const emit = defineEmits<Emits>()
</script>
