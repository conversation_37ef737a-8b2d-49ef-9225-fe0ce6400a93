import type { 
  StudentGrade, 
  GradingSession, 
  CriterionScore,
  RubricCriterion 
} from '~/types/grading'
import { RUBRIC_CRITERIA, TOTAL_POINTS } from '~/types/grading'

export const useGrading = () => {
  // Reactive state
  const currentSession = ref<GradingSession>({
    analystId: 0,
    studentName: '',
    criteria: [],
    overallComment: '',
    isDraft: true
  })

  const allGrades = ref<StudentGrade[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Initialize criteria scores
  const initializeCriteriaScores = () => {
    return RUBRIC_CRITERIA.map(criterion => ({
      criterionId: criterion.id,
      score: 0,
      comment: ''
    }))
  }

  // Load grades from localStorage
  const loadGrades = () => {
    try {
      const stored = localStorage.getItem('business-analyst-grades')
      if (stored) {
        const parsed = JSON.parse(stored)
        allGrades.value = parsed.map((grade: any) => ({
          ...grade,
          gradedAt: new Date(grade.gradedAt)
        }))
      }
    } catch (err) {
      error.value = 'Failed to load grades from storage'
      console.error('Error loading grades:', err)
    }
  }

  // Save grades to localStorage
  const saveGrades = () => {
    try {
      localStorage.setItem('business-analyst-grades', JSON.stringify(allGrades.value))
      error.value = null
    } catch (err) {
      error.value = 'Failed to save grades to storage'
      console.error('Error saving grades:', err)
    }
  }

  // Start new grading session
  const startGradingSession = (analystId: number, studentName?: string) => {
    currentSession.value = {
      analystId,
      studentName: studentName || '',
      criteria: initializeCriteriaScores(),
      overallComment: '',
      isDraft: true
    }
  }

  // Load existing grade for editing
  const loadExistingGrade = (analystId: number) => {
    const existingGrade = allGrades.value.find(grade => grade.analystId === analystId)
    if (existingGrade) {
      currentSession.value = {
        analystId: existingGrade.analystId,
        studentName: existingGrade.studentName || '',
        criteria: [...existingGrade.criteria],
        overallComment: existingGrade.overallComment,
        isDraft: false
      }
      return true
    }
    return false
  }

  // Update criterion score
  const updateCriterionScore = (criterionId: string, score: number, comment: string = '') => {
    const criterionIndex = currentSession.value.criteria.findIndex(c => c.criterionId === criterionId)
    if (criterionIndex >= 0) {
      currentSession.value.criteria[criterionIndex] = {
        criterionId,
        score,
        comment
      }
    }
  }

  // Calculate total score
  const calculateTotalScore = computed(() => {
    return currentSession.value.criteria.reduce((total, criterion) => total + criterion.score, 0)
  })

  // Calculate percentage
  const calculatePercentage = computed(() => {
    return Math.round((calculateTotalScore.value / TOTAL_POINTS) * 100)
  })

  // Get letter grade
  const getLetterGrade = computed(() => {
    const percentage = calculatePercentage.value
    if (percentage >= 90) return 'A'
    if (percentage >= 80) return 'B'
    if (percentage >= 70) return 'C'
    if (percentage >= 60) return 'D'
    return 'F'
  })

  // Save current grade
  const saveCurrentGrade = (gradedBy: string = 'Teacher') => {
    const grade: StudentGrade = {
      analystId: currentSession.value.analystId,
      studentName: currentSession.value.studentName,
      gradedAt: new Date(),
      criteria: [...currentSession.value.criteria],
      totalScore: calculateTotalScore.value,
      overallComment: currentSession.value.overallComment,
      gradedBy
    }

    // Remove existing grade if updating
    const existingIndex = allGrades.value.findIndex(g => g.analystId === grade.analystId)
    if (existingIndex >= 0) {
      allGrades.value[existingIndex] = grade
    } else {
      allGrades.value.push(grade)
    }

    saveGrades()
    currentSession.value.isDraft = false
    return grade
  }

  // Delete grade
  const deleteGrade = (analystId: number) => {
    const index = allGrades.value.findIndex(grade => grade.analystId === analystId)
    if (index >= 0) {
      allGrades.value.splice(index, 1)
      saveGrades()
      return true
    }
    return false
  }

  // Get grade by analyst ID
  const getGradeByAnalystId = (analystId: number) => {
    return allGrades.value.find(grade => grade.analystId === analystId)
  }

  // Check if student is graded
  const isStudentGraded = (analystId: number) => {
    return allGrades.value.some(grade => grade.analystId === analystId)
  }

  // Get class statistics
  const getClassStatistics = computed(() => {
    if (allGrades.value.length === 0) {
      return {
        totalStudents: 0,
        averageScore: 0,
        averagePercentage: 0,
        highestScore: 0,
        lowestScore: 0,
        gradeDistribution: { A: 0, B: 0, C: 0, D: 0, F: 0 }
      }
    }

    const scores = allGrades.value.map(grade => grade.totalScore)
    const percentages = scores.map(score => Math.round((score / TOTAL_POINTS) * 100))
    
    const gradeDistribution = { A: 0, B: 0, C: 0, D: 0, F: 0 }
    percentages.forEach(percentage => {
      if (percentage >= 90) gradeDistribution.A++
      else if (percentage >= 80) gradeDistribution.B++
      else if (percentage >= 70) gradeDistribution.C++
      else if (percentage >= 60) gradeDistribution.D++
      else gradeDistribution.F++
    })

    return {
      totalStudents: allGrades.value.length,
      averageScore: Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length),
      averagePercentage: Math.round(percentages.reduce((sum, pct) => sum + pct, 0) / percentages.length),
      highestScore: Math.max(...scores),
      lowestScore: Math.min(...scores),
      gradeDistribution
    }
  })

  // Initialize on composable creation
  onMounted(() => {
    loadGrades()
  })

  return {
    // State
    currentSession: readonly(currentSession),
    allGrades: readonly(allGrades),
    isLoading: readonly(isLoading),
    error: readonly(error),
    
    // Computed
    calculateTotalScore,
    calculatePercentage,
    getLetterGrade,
    getClassStatistics,
    
    // Methods
    startGradingSession,
    loadExistingGrade,
    updateCriterionScore,
    saveCurrentGrade,
    deleteGrade,
    getGradeByAnalystId,
    isStudentGraded,
    loadGrades,
    
    // Constants
    rubricCriteria: RUBRIC_CRITERIA,
    totalPoints: TOTAL_POINTS
  }
}
