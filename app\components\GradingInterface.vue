<template>
  <div class="space-y-6">
    <!-- Student Selection -->
    <UCard>
      <template #header>
        <h2 class="text-xl font-semibold">Grade Student Performance Task</h2>
      </template>
      
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UFormGroup label="Student Analyst ID" name="analystId" required>
            <UInput
              v-model.number="selectedAnalystId"
              type="number"
              placeholder="Enter student's Analyst ID (1-50)"
              min="1"
              max="50"
              @blur="loadStudentData"
            />
          </UFormGroup>
          
          <UFormGroup label="Student Name (Optional)" name="studentName">
            <UInput
              v-model="currentSession.studentName"
              placeholder="Enter student's name"
            />
          </UFormGroup>
        </div>

        <div v-if="selectedAnalystId > 0" class="flex items-center gap-4">
          <UButton 
            @click="startNewGrade"
            color="primary"
            :disabled="!selectedAnalystId"
          >
            Start New Grade
          </UButton>
          
          <UButton 
            v-if="isStudentGraded(selectedAnalystId)"
            @click="loadExistingGrade(selectedAnalystId)"
            color="gray"
            variant="outline"
          >
            Edit Existing Grade
          </UButton>
          
          <div v-if="isStudentGraded(selectedAnalystId)" class="text-sm text-green-600 dark:text-green-400">
            ✓ Student already graded
          </div>
        </div>
      </div>
    </UCard>

    <!-- Student Answers Display -->
    <div v-if="currentSession.analystId > 0">
      <StudentAnswers :analyst-id="currentSession.analystId" />
    </div>

    <!-- Grading Form -->
    <div v-if="currentSession.analystId > 0" class="space-y-6">
      <!-- Rubric Scoring -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold">Rubric Scoring</h2>
            <div class="text-right">
              <div class="text-2xl font-bold text-primary">
                {{ calculateTotalScore }}/{{ totalPoints }}
              </div>
              <div class="text-sm text-gray-500">
                {{ calculatePercentage }}% ({{ getLetterGrade }})
              </div>
            </div>
          </div>
        </template>

        <div class="space-y-6">
          <RubricScoring
            v-for="criterion in rubricCriteria"
            :key="criterion.id"
            :criterion="criterion"
            :current-score="getCriterionScore(criterion.id)"
            :current-comment="getCriterionComment(criterion.id)"
            @update-score="updateCriterionScore"
          />
        </div>
      </UCard>

      <!-- Overall Comments -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Overall Comments</h3>
        </template>
        
        <UFormGroup label="General feedback for the student" name="overallComment">
          <UTextarea
            v-model="currentSession.overallComment"
            placeholder="Enter overall feedback, suggestions for improvement, or commendations..."
            rows="4"
          />
        </UFormGroup>
      </UCard>

      <!-- Save Actions -->
      <UCard>
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-600 dark:text-gray-300">
            <span v-if="currentSession.isDraft" class="text-orange-600">Draft - Not saved</span>
            <span v-else class="text-green-600">✓ Saved</span>
          </div>
          
          <div class="flex gap-3">
            <UButton
              @click="clearSession"
              color="gray"
              variant="outline"
            >
              Clear
            </UButton>
            
            <UButton
              @click="saveGrade"
              color="primary"
              :loading="isLoading"
              :disabled="!canSaveGrade"
            >
              {{ isStudentGraded(currentSession.analystId) ? 'Update Grade' : 'Save Grade' }}
            </UButton>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Instructions -->
    <UCard v-if="currentSession.analystId === 0" class="text-center">
      <div class="py-8">
        <UIcon name="i-heroicons-academic-cap" class="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Start Grading
        </h3>
        <p class="text-gray-500 dark:text-gray-400">
          Enter a student's Analyst ID above to begin grading their performance task.
        </p>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
const { 
  currentSession,
  calculateTotalScore,
  calculatePercentage,
  getLetterGrade,
  startGradingSession,
  loadExistingGrade,
  updateCriterionScore,
  saveCurrentGrade,
  isStudentGraded,
  rubricCriteria,
  totalPoints,
  isLoading
} = useGrading()

const selectedAnalystId = ref(0)

// Get criterion score from current session
const getCriterionScore = (criterionId: string) => {
  const criterion = currentSession.value.criteria.find(c => c.criterionId === criterionId)
  return criterion?.score || 0
}

// Get criterion comment from current session
const getCriterionComment = (criterionId: string) => {
  const criterion = currentSession.value.criteria.find(c => c.criterionId === criterionId)
  return criterion?.comment || ''
}

// Load student data when analyst ID changes
const loadStudentData = () => {
  if (selectedAnalystId.value > 0 && selectedAnalystId.value <= 50) {
    // Check if there's an existing grade
    if (isStudentGraded(selectedAnalystId.value)) {
      // Don't auto-load, let teacher choose
      return
    }
  }
}

// Start new grading session
const startNewGrade = () => {
  if (selectedAnalystId.value > 0) {
    startGradingSession(selectedAnalystId.value, currentSession.value.studentName)
  }
}

// Clear current session
const clearSession = () => {
  selectedAnalystId.value = 0
  startGradingSession(0)
}

// Check if grade can be saved
const canSaveGrade = computed(() => {
  return currentSession.value.analystId > 0 && 
         currentSession.value.criteria.some(c => c.score > 0)
})

// Save grade
const saveGrade = () => {
  if (canSaveGrade.value) {
    saveCurrentGrade()
    // Show success notification
    const toast = useToast()
    toast.add({
      title: 'Grade Saved',
      description: `Grade for Analyst ID ${currentSession.value.analystId} has been saved successfully.`,
      color: 'green'
    })
  }
}
</script>
