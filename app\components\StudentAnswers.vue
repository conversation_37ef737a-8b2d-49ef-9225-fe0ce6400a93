<template>
  <UCard>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold">Correct Answers for Analyst ID: {{ analystId }}</h2>
        <UBadge color="green" variant="solid">
          Reference Solutions
        </UBadge>
      </div>
      <p class="text-sm text-gray-600 dark:text-gray-300 mt-2">
        Use these calculated answers to compare with the student's submitted work.
      </p>
    </template>

    <div class="space-y-6">
      <!-- Task 1: Cost Analysis -->
      <div class="border-l-4 border-blue-500 pl-4">
        <h3 class="font-semibold text-gray-900 dark:text-white mb-3">
          Task 1: Cost Analysis (Addition)
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div class="space-y-2">
            <div><strong>1. Total cost function:</strong> C(x) = {{ task1.results[0].answer }}</div>
            <div><strong>2. Cost of 10 units:</strong> {{ task1.results[1].answer }}</div>
            <div><strong>3. Cost of 50 units:</strong> {{ task1.results[2].answer }}</div>
          </div>
          <div class="space-y-2">
            <div><strong>4. Additional cost (10→50):</strong> {{ task1.results[3].answer }}</div>
            <div><strong>5. Cost if A doubled:</strong> {{ task1.results[4].answer }}</div>
          </div>
        </div>
      </div>

      <!-- Task 2: Profit Analysis -->
      <div class="border-l-4 border-green-500 pl-4">
        <h3 class="font-semibold text-gray-900 dark:text-white mb-3">
          Task 2: Profit Analysis (Subtraction)
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div class="space-y-2">
            <div><strong>1. Profit function:</strong> P(x) = {{ task2.results[0].answer }}</div>
            <div><strong>2. Profit (10 units):</strong> {{ task2.results[1].answer }}</div>
            <div><strong>3. Profit (100 units):</strong> {{ task2.results[2].answer }}</div>
          </div>
          <div class="space-y-2">
            <div><strong>4. Marginal profit:</strong> {{ task2.results[3].answer }}</div>
            <div><strong>5. Profit (1 unit):</strong> {{ task2.results[4].answer }}</div>
          </div>
        </div>
      </div>

      <!-- Task 3: Unit Revenue Analysis -->
      <div class="border-l-4 border-purple-500 pl-4">
        <h3 class="font-semibold text-gray-900 dark:text-white mb-3">
          Task 3: Unit Revenue Analysis (Multiplication)
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div class="space-y-2">
            <div><strong>1. R(x) verification:</strong> {{ task3.results[0].answer }}</div>
            <div><strong>2. Sister product R₂(x):</strong> {{ task3.results[1].answer }}</div>
            <div><strong>3. Revenue (100 sister units):</strong> {{ task3.results[2].answer }}</div>
          </div>
          <div class="space-y-2">
            <div><strong>4. Revenue (200 sister units):</strong> {{ task3.results[3].answer }}</div>
            <div><strong>5. Price effect:</strong> {{ task3.results[4].answer }}</div>
          </div>
        </div>
      </div>

      <!-- Task 4: Efficiency Analysis -->
      <div class="border-l-4 border-orange-500 pl-4">
        <h3 class="font-semibold text-gray-900 dark:text-white mb-3">
          Task 4: Efficiency Analysis (Division)
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div class="space-y-2">
            <div><strong>1. Average profit function:</strong> AP(x) = {{ task4.results[0].answer }}</div>
            <div><strong>2. AP (20 units):</strong> {{ task4.results[1].answer }}</div>
            <div><strong>3. AP (50 units):</strong> {{ task4.results[2].answer }}</div>
          </div>
          <div class="space-y-2">
            <div><strong>4. AP (100 units):</strong> {{ task4.results[3].answer }}</div>
            <div><strong>5. Limit as x→∞:</strong> {{ task4.results[4].answer }}</div>
          </div>
        </div>
      </div>

      <!-- Task 5: Net Bonus Analysis -->
      <div class="border-l-4 border-red-500 pl-4">
        <h3 class="font-semibold text-gray-900 dark:text-white mb-3">
          Task 5: Net Bonus Analysis (Composition)
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div class="space-y-2">
            <div><strong>1. Net bonus function:</strong> {{ task5.results[0].answer }}</div>
            <div><strong>2. Bonus (₱20,000 revenue):</strong> {{ task5.results[1].answer }}</div>
            <div><strong>3. Bonus (₱50,000 revenue):</strong> {{ task5.results[2].answer }}</div>
          </div>
          <div class="space-y-2">
            <div><strong>4. Intermediate B(R(x)):</strong> {{ task5.results[3].answer }}</div>
            <div><strong>5. New composite N₂(x):</strong> {{ task5.results[4].answer }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Reference -->
    <template #footer>
      <div class="text-xs text-gray-500 dark:text-gray-400">
        <strong>Quick Reference:</strong> A = {{ analystId }} | 
        F(x) = 100x + {{ 50 * analystId }} | 
        V(x) = 2x² + {{ 2 * analystId }}x | 
        C(x) = 2x² + {{ 100 + 2 * analystId }}x + {{ 50 * analystId }} | 
        P(x) = -5x² + {{ 900 - 2 * analystId }}x - {{ 50 * analystId }}
      </div>
    </template>
  </UCard>
</template>

<script setup lang="ts">
interface Props {
  analystId: number
}

const props = defineProps<Props>()

// Use the business analysis composable to get calculated answers
const { task1, task2, task3, task4, task5 } = useBusinessAnalysis()

// Set the analyst ID to get the correct calculations
const { analystId: businessAnalystId } = useBusinessAnalysis()
businessAnalystId.value = props.analystId
</script>
