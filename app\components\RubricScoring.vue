<template>
  <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
    <!-- Criterion Header -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex-1">
        <h4 class="font-semibold text-gray-900 dark:text-white">
          {{ criterion.name }}
        </h4>
        <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
          {{ criterion.description }}
        </p>
      </div>
      <div class="ml-4 text-right">
        <div class="text-lg font-bold text-primary">
          {{ currentScore }}/{{ criterion.maxPoints }}
        </div>
        <div class="text-xs text-gray-500">
          {{ Math.round((currentScore / criterion.maxPoints) * 100) }}%
        </div>
      </div>
    </div>

    <!-- Score Selection -->
    <div class="space-y-3">
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Score (0-{{ criterion.maxPoints }} points)
        </label>
        <USelect
          :model-value="currentScore"
          :options="scoreOptions"
          placeholder="Select score..."
          @update:model-value="updateScore"
        />
      </div>

      <!-- Score Description -->
      <div v-if="selectedLevel" class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <p class="text-sm text-blue-700 dark:text-blue-300">
          <strong>{{ selectedLevel.points }} points:</strong> {{ selectedLevel.description }}
        </p>
      </div>

      <!-- Comments -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Comments for {{ criterion.name }}
        </label>
        <UTextarea
          :model-value="currentComment"
          placeholder="Add specific feedback for this criterion..."
          :rows="2"
          @update:model-value="updateComment"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RubricCriterion } from '~/types/grading'

interface Props {
  criterion: RubricCriterion
  currentScore: number
  currentComment: string
}

interface Emits {
  updateScore: [criterionId: string, score: number, comment: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Create score options from criterion levels
const scoreOptions = computed(() => {
  const options = props.criterion.levels.map(level => ({
    label: `${level.points} points - ${level.description.split(':')[0]}`,
    value: level.points
  }))
  
  // Sort by points descending
  return options.sort((a, b) => b.value - a.value)
})

// Get selected level description
const selectedLevel = computed(() => {
  return props.criterion.levels.find(level => level.points === props.currentScore)
})

// Update score
const updateScore = (score: number) => {
  emit('updateScore', props.criterion.id, score, props.currentComment)
}

// Update comment
const updateComment = (comment: string) => {
  emit('updateScore', props.criterion.id, props.currentScore, comment)
}
</script>
