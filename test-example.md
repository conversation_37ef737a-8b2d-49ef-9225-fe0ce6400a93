# Business Analysis App Test Example

## Test with Analyst ID = 7

When you input A = 7 into the app, here are the expected results:

### Task 1: Cost Analysis (Addition)
1. **Total cost function C(x)**: C(x) = 2x² + 114x + 350
2. **Cost of 10 units**: ₱1,690.00
3. **Cost of 50 units**: ₱10,950.00
4. **Additional cost (10 to 50 units)**: ₱9,260.00
5. **Cost for 10 units if A doubled**: ₱2,040.00

### Task 2: Profit Analysis (Subtraction)
1. **Profit function P(x)**: P(x) = -5x² + 886x - 350
2. **Profit for 10 units**: ₱8,210.00
3. **Profit for 100 units**: ₱38,250.00
4. **Marginal profit (10 to 100 units)**: ₱30,040.00
5. **Profit for 1 unit**: ₱531.00

### Task 3: Unit Revenue Analysis (Multiplication)
1. **Verification**: R(x) = x * p(x) = x * (1000 - 3x) = 1000x - 3x² ✓
2. **Sister product revenue**: R₂(x) = 500x - x²
3. **Revenue from 100 sister units**: ₱40,000.00
4. **Revenue from 200 sister units**: ₱60,000.00
5. **Price effect**: Higher quantities reduce unit price, affecting total revenue

### Task 4: Efficiency Analysis (Division)
1. **Average profit function**: AP(x) = -5x + 886 - 350/x
2. **Average profit (20 units)**: ₱769.50
3. **Average profit (50 units)**: ₱779.00
4. **Average profit (100 units)**: ₱382.50
5. **Limit as x → ∞**: Approaches negative infinity

### Task 5: Net Bonus Analysis (Composition)
1. **Net bonus function**: N(x) = -0.255x² + 85x - 425
2. **Net bonus for ₱20,000 revenue**: Calculated based on solving R(x) = 20,000
3. **Net bonus for ₱50,000 revenue**: Calculated based on solving R(x) = 50,000
4. **Intermediate B(R(x))**: Represents gross bonus before tax
5. **New composite with flat tax**: N₂(x) = -0.3x² + 100x - 1500

## How to Use the App

1. Open http://localhost:3000 in your browser
2. Enter your class roll number (e.g., 7) in the "Analyst ID (A)" field
3. All calculations will automatically update and display
4. Each task shows the questions, answers, and step-by-step calculations
5. The app is responsive and works on mobile devices

## Features

- ✅ Real-time calculations based on your unique Analyst ID
- ✅ Clear presentation of all 5 tasks
- ✅ Step-by-step calculations shown
- ✅ Professional report format
- ✅ Dark/light mode support
- ✅ Mobile-responsive design
- ✅ Currency formatting for monetary values
- ✅ Mathematical function notation
