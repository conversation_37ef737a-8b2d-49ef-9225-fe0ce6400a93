{"name": "nuxt", "private": true, "type": "module", "packageManager": "pnpm@10.14.0", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "typecheck": "nuxt typecheck", "format": "biome format --write .", "lint": "biome lint --write ."}, "dependencies": {"@nuxt/ui": "^3.3.0", "@pinia/nuxt": "^0.11.2", "@vueuse/motion": "^3.0.3", "nuxt": "^4.0.2", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@biomejs/biome": "^2.1.3", "typescript": "^5.9.2", "vue-tsc": "^3.0.5"}}